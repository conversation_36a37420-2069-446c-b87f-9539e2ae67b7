services:
  # Setup container that creates folder structure and config files
  setup:
    image: alpine:latest
    container_name: pangolin-setup
    volumes:
      - ./:/host-setup
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - DOMAIN=${DOMAIN:-}
      - EMAIL=${EMAIL:-}
      - ADMIN_USERNAME=${ADMIN_USERNAME:-}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-}
      - ADMIN_SUBDOMAIN=${ADMIN_SUBDOMAIN:-pangolin}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_HOST=${POSTGRES_HOST:-pangolin-postgres}
      - GITHUB_USER=${GITHUB_USER:-oidebrett}
      - GITHUB_REPO=${GITHUB_REPO:-manidae}
      - GITHUB_BRANCH=${GITHUB_BRANCH:-main}
      - CROWDSEC_ENROLLMENT_KEY=${CROWDSEC_ENROLLMENT_KEY:-}
      - STATIC_PAGE_DOMAIN=${STATIC_PAGE_DOMAIN:-}
      - CLIENT_ID=${CLIENT_ID:-}
      - CLIENT_SECRET=${CLIENT_SECRET:-}
      - OAUTH_DOMAIN=oauth.${DOMAIN}
    command: |
      sh -c "
        # This will be set to true to enable CrowdSec config setup only if you have provided a CROWDSEC_ENROLLMENT_KEY
        ENABLE_CROWDSEC=false
        if [ -n \"$CROWDSEC_ENROLLMENT_KEY\" ]; then
          echo 'Crowdsec Selected for setup ...'
          ENABLE_CROWDSEC=true
        fi        
        echo '🚀 Starting Pangolin setup container...'

        # Install required tools
        apk add --no-cache curl docker-cli openssl
        
        # Validate required environment variables
        if [ -z \"$$DOMAIN\" ] || [ -z \"$$EMAIL\" ] || [ -z \"$$ADMIN_USERNAME\" ] || [ -z \"$$ADMIN_PASSWORD\" ]; then
          echo '❌ Error: Required environment variables not set!'
          echo 'Usage: DOMAIN=example.com EMAIL=<EMAIL> ADMIN_USERNAME=<EMAIL> ADMIN_PASSWORD=mypassword docker compose -f docker-compose-setup.yml up'
          echo 'Required variables:'
          echo '  DOMAIN - Your domain name (e.g., example.com)'
          echo '  EMAIL - Email for Lets Encrypt certificates'
          echo '  ADMIN_USERNAME - Admin username for Pangolin (usually email)'
          echo '  ADMIN_PASSWORD - Admin password for Pangolin (min 8 chars)'
          echo 'Optional variables:'
          echo '  ADMIN_SUBDOMAIN - Subdomain for admin portal (default: pangolin)'
          exit 1
        fi

        # Check if config folder already exists
        if [ -d \"/host-setup/config\" ]; then
          echo '⚠️ Config folder already exists!'
          echo 'To avoid overwriting your configuration, setup will not proceed.'
          echo 'If you want to run setup again, please remove or rename the existing config folder.'
          exit 1
        fi

        # Validate domain format
        if ! echo \"$$DOMAIN\" | grep -E '^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\\.[a-zA-Z]{2,}$$' > /dev/null; then
          echo '❌ Error: Invalid domain format'
          exit 1
        fi

        # Validate email format
        if ! echo \"$$EMAIL\" | grep -E '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$$' > /dev/null; then
          echo '❌ Error: Invalid email format'
          exit 1
        fi

        # Validate username as email format
        if ! echo \"$$ADMIN_USERNAME\" | grep -E '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$$' > /dev/null; then
          echo '❌ Error: Invalid admin username email format'
          exit 1
        fi

        # Validate password length
        if [ $${#ADMIN_PASSWORD} -lt 8 ]; then
          echo '❌ Error: Password must be at least 8 characters long'
          exit 1
        fi

        echo '✅ Environment variables validated'

        chmod +x /host-setup/container-setup.sh
        echo '✅ Container Setup script permissions set'

        # Run the setup script
        echo '🔧 Running setup script...'
        /host-setup/container-setup.sh

        # Create compose.yaml for services
        echo '📝 Creating compose.yaml for services...'
        cat > /host-setup/compose.yaml << 'EOF'
        services:
          # Main Pangolin application
          pangolin:
            image:  fosrl/pangolin:postgresql-latest
            container_name: pangolin
            restart: unless-stopped
            volumes:
              - ./config:/app/config
            healthcheck:
              test: ["CMD", "curl", "-f", "http://localhost:3001/api/v1/"]
              interval: "3s"
              timeout: "3s"
              retries: 15
      EOF

        # Part 1: Conditionally append standalone postgres if POSTGRES_HOST not the komodo postgres
        if [ -z "$POSTGRES_HOST" ] || [ "$POSTGRES_HOST" != "komodo-postgres-1" ]; then
          echo '🛡️ Adding Standalone Postgres database...'
          cat >> /host-setup/compose.yaml << EOF

            depends_on:
              postgres:
                condition: service_healthy

          # postgres for pangoln
          postgres:
            image: postgres:17
            container_name: pangolin-postgres
            restart: unless-stopped
            environment:
              POSTGRES_USER: ${POSTGRES_USER:-postgres} 
              POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
              POSTGRES_DB: postgres
            volumes:
              - ./config/postgres:/var/lib/postgresql/data
            healthcheck:
              test: [\"CMD-SHELL\", \"pg_isready -U ${POSTGRES_USER:-postgres}\"]
              interval: 10s
              timeout: 5s
              retries: 5

      EOF

        fi

        # Append gerbil block (always)
        cat >> /host-setup/compose.yaml << EOF

          # Auth management
          mcpauth:
            image: oideibrett/mcpauth:latest
            container_name: mcpauth
            environment:
              - PORT=11000
              - CLIENT_ID=REPLACE_WITH_CLIENT_ID
              - CLIENT_SECRET=REPLACE_WITH_CLIENT_SECRET
              - OAUTH_DOMAIN=${OAUTH_DOMAIN:-oauth.${DOMAIN}}
            restart: unless-stopped
            ports:
              - "11000:11000"

          # Gerbil WireGuard management
          gerbil:
            image: fosrl/gerbil:1.0.0
            container_name: gerbil
            restart: unless-stopped
            depends_on:
              pangolin:
                condition: service_healthy
            command:
              - --reachableAt=http://gerbil:3003
              - --generateAndSaveKeyTo=/var/config/key
              - --remoteConfig=http://pangolin:3001/api/v1/gerbil/get-config
              - --reportBandwidthTo=http://pangolin:3001/api/v1/gerbil/receive-bandwidth
            volumes:
              - ./config/:/var/config
            cap_add:
              - NET_ADMIN
              - SYS_MODULE
            ports:
              - 51820:51820/udp
              - 443:443 # Port for traefik because of the network_mode
              - 80:80 # Port for traefik because of the network_mode

          # Traefik reverse proxy
          traefik:
            image: traefik:v3.4.1
            container_name: traefik
            restart: unless-stopped
            network_mode: service:gerbil # Ports appear on the gerbil service
            depends_on:
              pangolin:
                condition: service_healthy
            command:
              - --configFile=/etc/traefik/traefik_config.yml
            volumes:
              - ./config/traefik:/etc/traefik:ro # Volume to store the Traefik configuration
              - ./config/letsencrypt:/letsencrypt # Volume to store the Lets Encrypt certificates
              - ./config/traefik/rules:/rules
              - ./config/traefik/logs:/var/log/traefik
              - ./public_html:/var/www/html:ro
              
          # Middleware Manager for Traefik / Pangolin
          middleware-manager:
            image: hhftechnology/middleware-manager:v3.0.1
            container_name: middleware-manager
            restart: unless-stopped
            volumes:
              - ./data:/data
              - ./config/traefik/rules:/conf
              - ./config/middleware-manager:/app/config
              - ./config/traefik:/etc/traefik
            environment:
              - PANGOLIN_API_URL=http://pangolin:3001/api/v1
              - TRAEFIK_CONF_DIR=/conf
              - DB_PATH=/data/middleware.db
              - PORT=3456
              - ACTIVE_DATA_SOURCE=pangolin
              - TRAEFIK_STATIC_CONFIG_PATH=/etc/traefik/traefik_config.yml
              - PLUGINS_JSON_URL=https://raw.githubusercontent.com/hhftechnology/middleware-manager/traefik-int/plugin/plugins.json
      #      ports:
      #        - "3456:3456"

          # Test http server - can be removed
          python-http:
            image: python:3.11-slim
            container_name: python-http
            working_dir: /app
            command: python -m http.server 15000
            ports:
              - "15000:15000"
            restart: unless-stopped

      EOF

        # Part 2: Conditionally append CrowdSec if CROWDSEC_ENROLLMENT_KEY is set
        if [ -n \"$CROWDSEC_ENROLLMENT_KEY\" ]; then
          echo '🛡️ Adding CrowdSec configuration...'
          cat >> /host-setup/compose.yaml << EOF

          crowdsec:
            image: crowdsecurity/crowdsec:latest
            container_name: crowdsec
            environment:
              GID: "1000"
              COLLECTIONS: crowdsecurity/traefik crowdsecurity/appsec-virtual-patching crowdsecurity/appsec-generic-rules crowdsecurity/linux
              ENROLL_INSTANCE_NAME: "pangolin-crowdsec"
              PARSERS: crowdsecurity/whitelists
              ENROLL_TAGS: docker
              ENROLL_KEY: "$$CROWDSEC_ENROLLMENT_KEY"
            healthcheck:
              interval: 10s
              retries: 15
              timeout: 10s
              test: ["CMD", "cscli", "capi", "status"]
            labels:
              - "traefik.enable=false" # Disable traefik for crowdsec
            volumes:
              # crowdsec container data
              - ./config/crowdsec:/etc/crowdsec # crowdsec config
              - ./config/crowdsec/db:/var/lib/crowdsec/data # crowdsec db
              # log bind mounts into crowdsec
              - ./config/traefik/logs:/var/log/traefik # traefik logs
            ports:
              - 6060:6060 # metrics endpoint for prometheus
            restart: unless-stopped
            command: -t # Add test config flag to verify configuration

      EOF

        fi


        # Part 3: Conditionally make docker use externa network if POSTGRES_HOST not the komodo postgres
        if [ -z "$POSTGRES_HOST" ] || [ "$POSTGRES_HOST" != "komodo-postgres-1" ]; then
          echo '🛡️ Adding Standalone Postgres database...'
          cat >> /host-setup/compose.yaml << EOF

        networks:
          default:
            driver: bridge
            name: pangolin
      EOF

        else
        # Append network block (always)
        cat >> /host-setup/compose.yaml << EOF
        
        networks:
          default:
            external: true
            name: pangolin

      EOF

        fi

         # Replace placeholders with actual variable syntax
        sed -i 's/REPLACE_WITH_CLIENT_ID/$$\{CLIENT_ID}/g' /host-setup/compose.yaml
        sed -i 's/REPLACE_WITH_CLIENT_SECRET/$$\{CLIENT_SECRET}/g' /host-setup/compose.yaml

        echo '✅ Setup completed! The stack is ready to start.'
        echo '📊 Start your services with: docker compose up -d'
        echo '🌐 Access at: https://'"$$ADMIN_SUBDOMAIN"'.'"$$DOMAIN"
        echo '👤 Admin login: '"$$ADMIN_USERNAME"

        # Keep container running briefly to show completion message
        sleep 5

      "
    restart: "no"
